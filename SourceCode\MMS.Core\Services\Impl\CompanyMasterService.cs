using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using MMS.Core.CoreUTI;
using System.Collections.Generic;
using System.Linq;

namespace MMS.Core.Services.Impl
{
    public class CompanyMasterService : BaseService<MerchantCompany>, ICompanyMasterService
    {
        private readonly IBaseRepository<SecurityLevelMaster> _securityLevelMasterRepository;
        private readonly IUsersCompanyRepository _usersCompanyRepository;

        public CompanyMasterService(
            IBaseRepository<MerchantCompany> repository, 
            ILanguageExecuteService languageExecuteService,
            IBaseRepository<SecurityLevelMaster> securityLevelMasterRepository,
            IUsersCompanyRepository usersCompanyRepository) : base(repository, languageExecuteService)
        {
            _securityLevelMasterRepository = securityLevelMasterRepository;
            _usersCompanyRepository = usersCompanyRepository;
        }

        #region Company Master Function By Language

        public IList<MerchantCompany> GetAll(bool includeDeactivated = false)
        {
            return base.GetAll(whereClauses: null, includeDeleted: false, includeDeactivated: includeDeactivated, orderBy: m => m.Name, isDescendingOrder: false);
        }

        public IList<MerchantCompany> GetAll(string search)
        {
            return base.GetAll(x => x.Name.Contains(search ?? ""), includeDeleted: false, orderBy: m => m.Name);
        }

        public override void Insert(MerchantCompany entity , bool isComit = true)
        {
            entity.IsActive = true;
            base.Insert(entity, isComit);

            _securityLevelMasterRepository.Insert(new SecurityLevelMaster(Constants.NONE, entity.Id));
            _securityLevelMasterRepository.Insert(new SecurityLevelMaster(Constants.SERVER, entity.Id));
            _securityLevelMasterRepository.Insert(new SecurityLevelMaster(Constants.MANAGER, entity.Id));
            _securityLevelMasterRepository.Insert(new SecurityLevelMaster(Constants.ADMIN, entity.Id));
        }

        #endregion


        #region CompanyMaster Base Function

        public int LastCompanyMaster()
        {
            var firstOrDefault = base.GetAll( null, false, true, m => m.Id.ToString(), false).FirstOrDefault();
            return firstOrDefault != null ? firstOrDefault.Id : 0;
        }

        #endregion

        public IList<ViewItemModel> GetViewListCompanyMasterByLang(int skip, int take, string searchKey, string ids)
        {
            return base.GetListItemByLang(p => p.Name, searchKey ?? "", skip, take);
        }

        #region Get List For Selection Item
        public IList<ViewItemModel> GetPersonsList(int editingId, int skip, int take, string searchKey, bool isDirector = false)
        {
            var details = _usersCompanyRepository.GetListForSelect().ToList();
            var result = new List<ViewItemModel>();

            _languageExecuteService.GetLanguage(details);

            foreach (var item in details)
            {
                result.Add(new ViewItemModel() { 
                    Id = item.Id, Label = item.FullName, 
                    IsActive = item.UsersCompany.Any(p => p.IsDirector == isDirector && p.CompanyDetailId == editingId) });
            }
            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            result = result.OrderBy(p=>p.Label).Skip(skip).Take(take).ToList();

            return result;
        }

        public IList<ViewItemModel> GetPersonsSelectedList( int editingId,int take, int skip, string searchKey, bool IsDirector = false, bool IsListView = false)
        {
            var userCompanies = _usersCompanyRepository.GetListSelected(editingId, IsDirector);
            var result = new List<ViewItemModel>();

            _languageExecuteService.GetLanguage(userCompanies.Select(p=>p.UserMaster).ToList());

            foreach (var item in userCompanies)
            {
                result.Add(new ViewItemModel()
                {
                    Id = IsListView? item.Id : item.UserMaster.Id,
                    IsActive = IsListView ? item.IsActive : item.UserMaster.IsActive,
                    Label = item.UserMaster.FullName,
                });
            }
            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            result = result.OrderBy(p => p.Label).Skip(skip).Take(take).ToList();

            return result;
        }

        #endregion


    }
}