using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MMS.Core.CoreUTI;
using MMS.Core.Services.Base;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace MMS.Core.dbContext
{
    public class DbContextFactory : IDbContextFactory
    {
        private readonly static ConcurrentDictionary<string, MMSContext> _dbContextDic = new();
        private readonly string _currentSectionId;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DbContextFactory> _logger;

        public DbContextFactory(IServiceProvider serviceProvider, ILogger<DbContextFactory> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;

            if (HttpContext.Current != null)
            {
                _currentSectionId = HttpContext.Current.Session.Id;
            }
            else
            {
                _currentSectionId = null;
            }
        }

        public MMSContext GetContext(bool isUsingSessionContext)
        {
            if (!isUsingSessionContext)
            {
                // Get a fresh scoped context from the DI container for each call
                // This ensures proper scoping and prevents threading conflicts
                var context = _serviceProvider.GetRequiredService<MMSContext>();
                _logger.LogInformation("DbContextFactory.GetContext: Returning scoped context {ContextId} for session {SessionId}",
                    context.GetHashCode(), _currentSectionId ?? "null");
                return context;
            }

            var result = _dbContextDic.GetValueOrDefault(_currentSectionId); // Get session ctx

            if (result == null)
            {
                result = this.CreateDbContext();
                _dbContextDic.TryAdd(_currentSectionId, result);
                _logger.LogInformation("DbContextFactory.GetContext: Created new session context {ContextId} for session {SessionId}",
                    result.GetHashCode(), _currentSectionId);
            }
            else
            {
                _logger.LogInformation("DbContextFactory.GetContext: Returning existing session context {ContextId} for session {SessionId}",
                    result.GetHashCode(), _currentSectionId);
            }

            return result;
        }

        public void CtxDisposeOrNot(MMSContext context, bool isUsingSessionContext)
        {
            if (!isUsingSessionContext)
            {
                // Don't dispose the scoped context as it's managed by DI container
                // The DI container will handle disposal automatically
                return;
            }
        }

        private MMSContext CreateDbContext()
        {
            return CreateDbContext(null);
        }

        public void RemoveContext()
        {
            if (string.IsNullOrWhiteSpace(_currentSectionId))
                return;
            _dbContextDic.Remove(_currentSectionId, out var mmsContext);
        }

        public MMSContext CreateDbContext(string[] args)
        {
            var builder = new DbContextOptionsBuilder<MMSContext>();
            builder.ConfigureWarnings(warnings => warnings.Ignore(CoreEventId.NavigationBaseIncludeIgnored, CoreEventId.NavigationBaseIncluded));

            var connectionString = Environment.GetEnvironmentVariable(MMS.Core.CoreUTI.Constants.ENV_MMS_DB_CONNECTION);

#if DEBUG
            // Fake connection if debug for by pass dotnet ef migrations add
            if (string.IsNullOrWhiteSpace(connectionString))
                connectionString = "Server=localhost;Port=3306;Database=mms;User=root;Password=password;";
#else
            if (string.IsNullOrWhiteSpace(connectionString)) throw new InvalidOperationException($"Can not find the MMS_DB_CONNECTION");
#endif

            var serverVersion = new MariaDbServerVersion(new Version(8, 0));

            builder.UseMySql(connectionString,
                serverVersion,
                providerOptions => providerOptions.EnableRetryOnFailure(
                                maxRetryCount: 10,
                                maxRetryDelay: TimeSpan.FromSeconds(30),
                                errorNumbersToAdd: null)
                                .CommandTimeout(120)
                );

            var ctx = new MMSContext(builder.Options);
            return ctx;
        }
    }
}
