using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.Entities;
using MMS.Core.Entities.Company;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MMS.Core.Services.Companies.Impl
{
    public class CompanyUserAccessService : BaseService<CompanyUserAccess>, ICompanyUserAccessService
    {
        public CompanyUserAccessService(IBaseRepository<CompanyUserAccess> repository, ILanguageExecuteService languageExecuteService) : base(repository, languageExecuteService)
        {
        }

        public IList<CompanyUserAccess> GetSelected(int companyId, bool useSelectSort)
        {
            var query = GetAll(p => p.CompanyId == companyId && p.OriginalCompanyId == null && p.UserAccess.IsActive && p.IsStatus != Constants.PRE_DELETE_RECORD && p.IsStatus != Constants.DELETE_RECORD, includes: p => p.UserAccess).AsQueryable();

            query = query.OrderBy(p => p.OrderIndex);

            return query.ToList();
        }

        public IList<CompanyUserAccess> GetCompanyByPerson(int userMaterId)
        {
            Expression<Func<CompanyUserAccess, object>>[] includes = { p => p.CompanyDetails, p => p.OriginalCompany };

            var query = GetAll(p => p.UserAccessId == userMaterId && p.UserAccess.IsActive && p.IsStatus != Constants.PRE_DELETE_RECORD && p.IsStatus != Constants.DELETE_RECORD,
                includes: includes).AsQueryable();

            query = query.OrderBy(p => p.OrderIndex);

            return query.ToList();
        }

        public void UpdateSelected(int companyId, List<int> selectedIds, bool useSelectSort)
        {
            var addableList = new List<CompanyUserAccess>();
            var configs = GetSelected(companyId, useSelectSort);

            var index = 1;
            var maxIndex = configs.Any() ? configs.Max(p => p.OrderIndex) + 1 : index;
            foreach (var each in selectedIds)
            {
                CompanyUserAccess obj = null;
                obj = configs.FirstOrDefault(p => p.UserAccessId == each);

                if (obj != null)
                {
                    if (!useSelectSort)
                    {
                        obj.OrderIndex = maxIndex++;
                    }
                }
                else
                {
                    obj = new CompanyUserAccess
                    {
                        IsActive = true,
                        UserAccessId = each,
                        OrderIndex = maxIndex++,
                        CompanyId = companyId
                    };

                    addableList.Add(obj);
                }

                if (useSelectSort)
                {
                    obj.OrderIndex = index++;
                }
            }

            var removeList = configs.Where(p => !selectedIds.Contains(p.UserAccessId)).ToList();

            foreach (var each in removeList)
            {
                Delete(each);
            }

            Delete(removeList);
            InsertAll(addableList);
            UpdateAll(configs);
        }

        public IList<ViewItemModel> GetViewListPersonsSelected(int skip, int take, string searchKey, int currentCompanyId)
        {
            return GetListSelectedItem(p => p.Name, p => p.CompanyId == currentCompanyId, searchKey ?? "", skip, take, includes: p => p.UserAccess);
        }

        public IList<CompanyUserAccess> GetSelectedByAssociatedCompanies(int companyId, int currentCompanyId, bool? useSelectSort = true)
        {
            var query = GetAll(p => p.CompanyId == companyId && p.OriginalCompanyId == currentCompanyId && p.UserAccess.IsActive && p.IsStatus != Constants.PRE_DELETE_RECORD && p.IsStatus != Constants.DELETE_RECORD, includes: p => p.UserAccess).AsQueryable();

            query = query.OrderBy(p => p.OrderIndex);

            return query.ToList();
        }

        public async Task<IList<CompanyUserAccess>> UpdatePersonsSelected(int companyId, int currentCompanyId, List<int> selectedIds, bool useSelectSort)
        {
            var addableList = new List<CompanyUserAccess>();
            var configs = GetSelectedByAssociatedCompanies(companyId, currentCompanyId);

            var index = 1;
            var maxIndex = configs.Any() ? configs.Max(p => p.OrderIndex) + 1 : index;
            foreach (var each in selectedIds)
            {
                CompanyUserAccess obj = null;
                obj = configs.FirstOrDefault(p => p.UserAccessId == each);

                if (obj != null)
                {
                    if (!useSelectSort)
                    {
                        obj.OrderIndex = maxIndex++;
                    }
                }
                else
                {
                    obj = new CompanyUserAccess
                    {
                        IsActive = true,
                        OriginalCompanyId = currentCompanyId,
                        UserAccessId = each,
                        OrderIndex = maxIndex++,
                        CompanyId = companyId
                    };

                    addableList.Add(obj);
                }

                if (useSelectSort)
                {
                    obj.OrderIndex = index++;
                }
            }

            var removeList = configs.Where(p => !selectedIds.Contains(p.UserAccessId)).ToList();
            configs = configs.Where(p => selectedIds.Contains(p.UserAccessId)).ToList(); // Update list.

            Delete(removeList);
            InsertAll(addableList);
            UpdateAll(configs);

            var result = new List<CompanyUserAccess>();

            //result.AddRange(configs);
            result.AddRange(addableList);

            return result; //All
        }

        public void DeleteAssociatedPersonSelected(int companyId, int currentCompanyId)
        {
            var query = GetAll(p => p.CompanyId == companyId && p.OriginalCompanyId == currentCompanyId, includes: p => p.UserAccess).AsQueryable();

            Delete(query);
        }

        public async Task<bool> CheckAccessCode(int companyId, int uid, string accessCode)
        {
            if (string.IsNullOrWhiteSpace(accessCode)) return false;
            var context = Repo.GetContext();
            {
                var q = from b in context.CompanyUserAccesses
                        where b.CompanyId == companyId
                        where b.UserAccessId == uid
                        where !b.IsDeactivateSelection
                        where b.AccessDateTime < DateTime.UtcNow
                        where b.ExpiryDateTime > DateTime.UtcNow
                        select b;

                var companyUserAccess = await q.FirstOrDefaultAsync();
                return companyUserAccess != null && CoreUtils.AesDecryptData(companyUserAccess.AccessCode) == accessCode;
            }
        }

        public async Task<CompanyUserAccess> GetByCompanyAndUser(int companyId, int userId, int? originalCompanyId)
        {
            var context = Repo.GetContext();
            var qCompanyUserAccess = from b in context.CompanyUserAccesses
                                     where b.CompanyId == companyId
                                     where b.OriginalCompanyId == originalCompanyId
                                     where b.UserAccessId == userId
                                     where !b.IsDeactivateSelection
                                     where b.IsActive
                                     select b;

            qCompanyUserAccess = qCompanyUserAccess.AsNoTracking();
            return await qCompanyUserAccess.FirstOrDefaultAsync();
        }
    }
}
