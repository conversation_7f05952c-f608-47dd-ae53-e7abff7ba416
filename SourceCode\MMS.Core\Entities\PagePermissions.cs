using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using System.ComponentModel.DataAnnotations.Schema;

namespace MMS.Core.Entities
{
    public class PagePermission : BaseEntity
    {
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public override int Id { get; set; }

        public int Function { get; set; }

        public int? FunctionParent { get; set; }

        public string Name { get; set; }

        public bool AddAction { get; set; }

        public bool EditAction { get; set; }

        public bool ActiveAction { get; set; }

        public bool ViewAction { get; set; }

        public bool SelectRemoveAction { get; set; }

        public bool RemoveAction { get; set; }

        public string HierarchyId { get; set; }

        public FilterGroupType Group { get; set; }

        public FilterDataTable DataTable { get; set; }

        /// <summary>
        /// Indicates the source(s) of this permission (Web, API, Mobile, etc.)
        /// </summary>
        public PermissionSource Source { get; set; } = PermissionSource.None;

        [NotMapped]
        public bool IsRoot { get; set; }
    }

    public class PagePermissionMap : IEntityTypeConfiguration<PagePermission>
    {
        public void Configure(EntityTypeBuilder<PagePermission> builder)
        {
            builder.ToTable(Constants.PagePermissions);
            builder.HasKey(c => c.Id);
        }
    }
}
