using MMS.Core.Services.Base;
using MMS.Core.Entities;
using MMS.Core.Repository;
using MMS.Core.CoreUTI;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace MMS.Core.Services.Impl.Base
{
    public class BaseService<T> : IBaseService<T> where T : BaseEntity
    {
        private readonly IBaseRepository<T> _repository;
        protected ILanguageExecuteService _languageExecuteService;
        protected IBaseRepository<T> Repo { get { return this._repository; } }
        private const string EntityNullErrorMessage = "Entity of type {0} is null";

        public BaseService(IBaseRepository<T> repository,
            ILanguageExecuteService languageExecuteService)
        {
            _repository = repository;
            _languageExecuteService = languageExecuteService;
        }

        public virtual IBaseService<T> UsingSessionContext()
        {
            _repository.UsingSessionContext();
            return this;
        }

        public virtual T GetById(int id, params Expression<Func<T, object>>[] includes)
        {
            if (_languageExecuteService == null)
            {
                return _repository.GetById(id, includes); //No language service
            }

            return _languageExecuteService.GetLanguage(_repository.GetById(id, includes));
        }

        public IList<T> GetById(IEnumerable<int> ids, bool includeDeleted = false, bool includeHardCoded = false, bool includeDeactivated = false, params Expression<Func<T, object>>[] includes)
        {
            if (ids == null)
            {
                return new List<T>();
            }

            var query = Repo.GetAll(includeDeleted, includeDeactivated, p => ids.Contains(p.Id), includes: includes);

            if (_languageExecuteService == null)
            {
                return query.ToList(); //No language service
            }

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IQueryable<T> GetPage(IQueryable<T> query, int pageSize, int pageIndex, Expression<Func<T, object>> sortExpression = null)
        {
            return _repository.GetPage(query, pageSize, pageIndex, sortExpression);
        }

        public virtual IList<T> GetAll(int pageSize = 0, int startIndex = 0, bool includeDeleted = false)
        {
            var query = GetPage(Repo.GetAll(includeDeactivated: true, includeDeleted: includeDeleted).AsQueryable(), pageSize: pageSize, pageIndex: startIndex);
            if (_languageExecuteService == null)
            {
                return query.ToList(); //No language service
            }

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public virtual IList<T> GetAll(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, bool includeDeactivated = true,
            Expression<Func<T, object>> orderBy = null, bool isDescendingOrder = false, params Expression<Func<T, object>>[] includes)
        {
            var query = Repo.GetAll(whereClause: whereClauses, includeDeleted: includeDeleted, includeDeactivated: includeDeactivated, includes: includes).AsQueryable();

            if (orderBy != null)
            {
                if (isDescendingOrder)
                    query = query.OrderByDescending(orderBy);
                else
                    query = query.OrderBy(orderBy);
            }

            if (_languageExecuteService == null)
            {
                return query.ToList(); //No language service
            }

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        /// <summary>
        ///  Retrieves all entities, prioritizing ChangeTracker entries if available.
        /// </summary>
        /// <returns></returns>
        public virtual IList<T> GetAllV2(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, bool includeDeactivated = true,
            Expression<Func<T, object>> orderBy = null, bool isDescendingOrder = false, params Expression<Func<T, object>>[] includes)
        {
            var query = Repo.GetAllV2(whereClause: whereClauses, includeDeleted: includeDeleted, includeDeactivated: includeDeactivated, includes: includes).AsQueryable();

            if (orderBy != null)
            {
                if (isDescendingOrder)
                    query = query.OrderByDescending(orderBy);
                else
                    query = query.OrderBy(orderBy);
            }

            if (_languageExecuteService == null)
            {
                return query.ToList(); //No language service
            }

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public virtual void Insert(T entity, bool isCommit = true)
        {
            if (entity == null)
            {
                throw new ArgumentNullException(string.Format(EntityNullErrorMessage , typeof(T).Name));
            }

            _repository.Insert(entity, isCommit);

            if (_languageExecuteService == null)
            {
                return;
            }

            _languageExecuteService.UpdateLanguage(entity);

        }

        public virtual void InsertAll(IList<T> entities)
        {
            foreach (var item in entities)
            {
                Insert(item, false);
            }

            _repository.SaveChanges();
        }

        public virtual void InsertAll(IList<T> entities, bool isCommit)
        {
            foreach (var item in entities)
            {
                Insert(item, isCommit);
            }
        }

        public virtual void Update(T entity, bool isCommit = true)
        {
            if (entity == null)
            {
                throw new ArgumentNullException(string.Format(EntityNullErrorMessage , typeof(T).Name));
            }

            _repository.Update(entity, isCommit);

            if (_languageExecuteService == null)
            {
                return;
            }

            _languageExecuteService.UpdateLanguage(entity);

        }

        public virtual void UpdateAll(IList<T> entities)
        {
            foreach (var item in entities)
            {
                Update(item, false);
            }

            _repository.SaveChanges();
        }

        public virtual void UpdateAll(IList<T> entities, bool isCommit)
        {
            foreach (var item in entities)
            {
                Update(item, isCommit);
            }
        }

        /// <summary>
        /// Add Or Update
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="isCommit"></param>
        /// <exception cref="NullReferenceException"></exception>
        public virtual void AddOrUpdate(T entity, bool isCommit = true)
        {
            if (entity == null)
            {
                throw new ArgumentNullException(string.Format(EntityNullErrorMessage , typeof(T).Name));
            }

            _repository.AddOrUpdate(entity, isCommit);

            if (_languageExecuteService == null)
            {
                return;
            }

            _languageExecuteService.UpdateLanguage(entity);
        }

        /// <summary>
        /// Add Or Update the list of entities
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="isCommit"></param>
        /// <exception cref="NullReferenceException"></exception>
        public virtual void AddOrUpdateAll(IList<T> entities, bool isCommit = true)
        {
            if (entities == null)
            {
                throw new ArgumentNullException(string.Format("Entities of type {0} is null", typeof(T).Name));
            }

            _repository.AddOrUpdateAll(entities, isCommit);

            if (_languageExecuteService == null)
            {
                return;
            }

            foreach (var entity in entities)
            {
                _languageExecuteService.UpdateLanguage(entity);
            }
        }

        public virtual void Delete(T entity, bool deleteFromData)
        {
            if (entity == null)
            {
                throw new ArgumentNullException(string.Format(EntityNullErrorMessage , typeof(T).Name));
            }

            _repository.Delete(entity, deleteFromData);

        }

        public void Delete(int id, bool deleteFromData = false)
        {
            Delete(GetById(id), deleteFromData);
        }

        public virtual void Delete(T entity)
        {
            if (entity == null)
            {
                throw new ArgumentNullException(string.Format(EntityNullErrorMessage , typeof(T).Name));
            }

            _repository.Delete(entity, false);

        }

        public virtual void Delete(IEnumerable<T> entities, bool deleteFromData = false)
        {
            if (entities == null)
            {
                throw new ArgumentNullException(string.Format("Entities of type {0} is null", typeof(T).Name));
            }
            foreach (var item in entities)
            {
                _repository.Delete(item, deleteFromData);
            }
        }

        public List<TU> RawSqlQuery<TU>(string query, Func<DbDataReader, TU> map)
        {
            return _repository.RawSqlQuery(query, map);
        }

        public IList<ViewItemModel> GetListItemByLangWithAdminAccess(Func<T, string> funcName, string searchKey, int skip, int take, IList<int> ids = null, bool isAdmin = false)
        {
            var result = new List<ViewItemModel>();
            var entities = Repo.GetAll(false, true, null, null).ToList();

            if (ids != null)
            {
                entities = entities.Where(p => ids.Contains(p.Id)).ToList();
            }

            _languageExecuteService.GetLanguage(entities);

            foreach (var item in entities)
            {
                result.Add(new ViewItemModel() { Id = item.Id, IsActive = item.IsActive, Label = funcName(item) });
            }

            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            return result.Skip(skip).Take(take).ToList();
        }

        public int GetItemNumberByLang(Func<T, string> funcName, string searchKey)
        {
            var result = new List<ViewItemModel>();
            var entities = Repo.GetAll(false, true, null, null).ToList();

            _languageExecuteService.GetLanguage(entities);

            foreach (var item in entities)
            {
                result.Add(new ViewItemModel() { Id = item.Id, IsActive = item.IsActive, Label = funcName(item) });
            }

            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            return result.Count;
        }

        public IList<ViewItemModel> GetListSelectedItemByLang(Func<T, string> nameFunc, string searchKey, int skip, int take, Expression<Func<T, bool>> whereClauses = null)
        {
            var result = new List<ViewItemModel>();
            var entities = Repo.GetAll(false, true, whereClauses, null).ToList();

            _languageExecuteService.GetLanguage(entities);

            foreach (var item in entities)
            {
                result.Add(new ViewItemModel() { Id = item.Id, IsActive = item.IsActive, Label = nameFunc(item) });
            }

            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            return result.Skip(skip).Take(take).ToList();
        }

        public IList<ViewItemModel> GetListItemByLang(Func<T, string> nameFunc, string searchKey, int skip, int take)
        {
            var result = new List<ViewItemModel>();

            var entities = Repo.GetAll(false, true, null, null).ToList();

            _languageExecuteService.GetLanguage(entities);

            foreach (var item in entities)
            {
                result.Add(new ViewItemModel() { Id = item.Id, IsActive = item.IsActive, Label = nameFunc(item) });
            }

            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            return result.Skip(skip).Take(take).ToList();
        }

        public IList<ViewItemModel> GetListSelectedItem(Func<T, string> nameFunc, Expression<Func<T, bool>> whereClauses, string searchKey, int skip, int take, params Expression<Func<T, object>>[] includes)
        {
            var result = new List<ViewItemModel>();

            var entities = Repo.GetAll(false, false, whereClauses, includes).ToList();

            _languageExecuteService.GetLanguage(entities);

            foreach (var item in entities)
            {
                result.Add(new ViewItemModel() { Id = item.Id, IsActive = item.IsActive, Label = nameFunc(item) });
            }

            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            return result.Skip(skip).Take(take).ToList();
        }

        public IList<string> CheckExistsRecord(string field, string value, Type type = null)
        {
            return _languageExecuteService.GetByObjectNameAndValue(type != null ? type : typeof(T), field, value) ?? new List<string>();
        }

        public IList<string> CheckExistsRecord(string field, string value, int objectId, Type type = null)
        {
            return _languageExecuteService.GetByObjectAndValue(type != null ? type : typeof(T), field, value, objectId) ?? new List<string>();
        }

        public IList<ViewItemModel> GetListItemsWithParentIdByLang(Func<T, string> nameFunc, string searchKey, int skip, int take, Expression<Func<T, bool>> whereClauses = null)
        {
            var result = new List<ViewItemModel>();
            var entities = whereClauses != null ? Repo.GetAll(false, true, whereClauses, null).ToList() : Repo.GetAll(false, true, null, null).ToList();

            _languageExecuteService.GetLanguage(entities);

            foreach (var item in entities)
            {
                result.Add(new ViewItemModel() { Id = item.Id, IsActive = item.IsActive, Label = nameFunc(item) });
            }

            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            result = result.OrderBy(p => p.Label).ToList();

            if (take != Constants.TakeAll)
            {
                return result.Skip(skip).Take(take).ToList();
            }

            return result.ToList();
        }

        /// <summary>
        /// Get first element
        /// </summary>
        /// <returns></returns>
        public virtual T FirstOrDefault(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes)
        {
            var result = _repository.FirstOrDefault(whereClauses, includeDeleted, includes);

            return _languageExecuteService.GetLanguage(result);
        }

        /// <summary>
        /// Get Last element in table
        /// </summary>
        /// <returns></returns>
        public virtual T LastOrDefault(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes)
        {
            var result = _repository.LastOrDefault(whereClauses, includeDeleted, includes);

            return _languageExecuteService.GetLanguage(result);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="selectClause"></param>
        /// <param name="whereClauses"></param>
        /// <param name="includeDeleted"></param>
        /// <returns></returns>
        public virtual IList<object> Select(Expression<Func<T, object>> selectClause = null, Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes)
        {
            return Repo.Select(selectClause, whereClauses, includeDeleted, includes);
        }

        public virtual IList<T> GetAllCheckActive(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, bool includeDeactivated = false ,Expression<Func<T, object>> orderBy = null, bool isDescendingOrder = false, params Expression<Func<T, object>>[] includes)
        {
            var query = Repo.GetAll(whereClause: whereClauses, includeDeleted: includeDeleted, includes: includes).AsQueryable();

            if (includeDeactivated)
            {
                query.Where(p => p.IsActive);
            }

            if (orderBy != null)
            {
                if (isDescendingOrder)
                    query = query.OrderByDescending(orderBy);
                else
                    query = query.OrderBy(orderBy);
            }

            if (_languageExecuteService == null)
            {
                return query.ToList(); //No language service
            }

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public virtual bool IsExisted(int id)
        {
            return _repository.IsExisted(id);
        }

        public IList<T> GetExitDatabase(Expression<Func<T, bool>> whereClauses = null)
        {
            var result = GetAll(whereClauses, includeDeleted: true);
            return result;
        }

        // return list insert because Insert using context basic other than session context Update
        public List<T> UpdateChangeReturnListInsert(int idIndex, List<int> idSelects, Expression<Func<T, bool>> whereClauses, string nameField)
        {
            //list vm == null
            T entity;

            var entitiesUpdate = new List<T>();
            var entitiesInsert = new List<T>();

            var entities = GetExitDatabase(whereClauses);

            ParameterExpression pe = Expression.Parameter(typeof(T), "p");
            MemberExpression me = Expression.Property(pe, nameField);

            foreach (int id in idSelects)
            {
                //create lamda p => p.nameField == id
                ConstantExpression constant = Expression.Constant(id, typeof(int));
                BinaryExpression body = Expression.Equal(me, constant);
                var expressionTree = Expression.Lambda<Func<T, bool>>(body, pe);

                 entity = entities.AsQueryable().FirstOrDefault(expressionTree);

                if (entity != null) // exist database
                {
                    entity.IsStatus = Constants.PRE_INSERT_RECORD;
                    entity.IsActive = true;
                    entitiesUpdate.Add(entity);
                }
                else // not exist database, vm is active
                {

                    var objectT = (T)Activator.CreateInstance(typeof(T), new object[] { idIndex, id, Constants.PRE_INSERT_RECORD });
                    objectT.IsActive = true;
                    entitiesInsert.Add(objectT);
                }
            }

            //create lamda p => !idSelects.Contains((int)p.nameField)
            var methodInfo = typeof(List<int>).GetMethod("Contains", new Type[] { typeof(int) });
            var list = Expression.Constant(idSelects);
            var bodyContains = Expression.Call(list, methodInfo, me);
            var expressionContains =  Expression.Lambda<Func<T, bool>>(Expression.Not(bodyContains), pe);


            var entitiesUnSelect = entities.AsQueryable().Where(expressionContains).ToList();
            foreach (var item in entitiesUnSelect)
            {
                item.IsActive = false;
            }

            UpdateAll(entitiesUnSelect, false);
            UpdateAll(entitiesUpdate, false);

            return entitiesInsert;
        }

        public IList<string> CheckNameExist(string input, int id)
        {
            return CheckExistsRecord("Name", input, id);
        }

        public IList<ViewItemModel> SearchByName(Func<T, string> nameFunc, string searchKey, Expression<Func<T, object>> sortExpression = null, Expression<Func<T, bool>> whereClauses = null, params Expression<Func<T, object>>[] includes)
        {
            var result = new List<ViewItemModel>();

            var entities = Repo.GetAll(false, true, whereClauses , includes).AsQueryable();

            if (sortExpression != null)
                entities.OrderBy(sortExpression);

            foreach (var item in entities.ToList())
            {
                result.Add(new ViewItemModel() { Id = item.Id, IsActive = item.IsActive, Label = nameFunc(item) });
            }

            _languageExecuteService.GetLanguage(entities.ToList());

            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey.Trim())).ToList();
            }

            return result;
        }

        public void UpdateDragSort(List<int> separateStringToInt, string nameField)
        {
            var addableList = new List<T>();

            var index = 1;

            foreach (var id in separateStringToInt)
            {
                var item = GetById(id);

                Type myType = item.GetType();

                PropertyInfo pinfo = myType.GetProperty(nameField);

                pinfo.SetValue(item, index++, null);

                addableList.Add(item);
            }

            this.UpdateAll(addableList);
        }

        public void InsertOrUpdate(T entity,bool isCommit = true)
        {
            if (entity.Id > 0)
            {
                Update(entity, isCommit);
            } else
            {
                entity.IsActive = true;
                Insert(entity, isCommit);
            }
        }

        public IEnumerable<T> GetFromChangeTracker()
        {
            var context = _repository.GetContext();

            return context.ChangeTracker.Entries<T>().Select(e => e.Entity);
        }
    }
}
