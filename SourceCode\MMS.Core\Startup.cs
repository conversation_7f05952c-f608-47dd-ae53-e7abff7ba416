using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using MMS.Core.CoreUTI;
using MMS.Core.dbContext;
using MMS.Core.Mappers;
using MMS.Core.Models;
using MMS.Core.Repository;
using MMS.Core.Repository.Impl;
using MMS.Core.Services;
using MMS.Core.Services.Base;
using MMS.Core.Services.Cache.Impl;
using MMS.Core.Services.Cache;
using MMS.Core.Services.Impl;
using MMS.Core.Services.Impl.Base;
using MMS.Core.Services.Jwt;
using MMS.Core.Services.Jwt.Impl;
using MMS.Core.Services.MessageEvent;
using MMS.Core.Services.MessageEvent.Impl;
using MMS.Core.Services.Paxstore;
using MMS.Core.Services.Paxstore.Impl;
using MMS.Core.Services.SyncTerminalSetup;
using MMS.Core.Services.SyncTerminalSetup.Impl;
using MMS.Paxstore.Impl;
using MMS.Paxstore.Interface;
using System;
using System.Linq;
using System.Reflection;
using System.Text;

namespace MMS.Core
{
    public static class Startup
    {
        public static void CoreConfigureRepository(this IServiceCollection services)
        {
            // Overrides or customizes transient repository registration (default: Transient)
        }

        public static void CoreConfigureService(this IServiceCollection services)
        {
            services.AddScoped<ISystemPaxstoreService, SystemPaxstoreService>();
        }

        public static void CoreConfigureAutoMapper(this IServiceCollection services)
        {
            services.AddAutoMapper(typeof(CompanyWebViewAutoMapper).Assembly);
            services.AddAutoMapper(typeof(WebViewMediaFileAutoMapper).Assembly);
            services.AddAutoMapper(typeof(WebViewFeatureAutoMapper).Assembly);
            services.AddAutoMapper(typeof(CopyTemplateProfile).Assembly);
        }

        public static void CoreConfigureSignalR(this IServiceCollection services)
        {
            services.AddSignalR();
        }

        public static void CoreRegisterRepositories(this IServiceCollection services)
        {
            var assembly = Assembly.GetExecutingAssembly();

            services.RegisterGenericServices(assembly, typeof(IBaseRepository<>));
            services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));
        }

        public static void CoreRegisterServices(this IServiceCollection services)
        {
            var assembly = Assembly.GetExecutingAssembly();

            services.RegisterGenericServices(assembly, typeof(IBaseService<>));
            services.AddScoped(typeof(IBaseService<>), typeof(BaseService<>));
        }

        private static void RegisterGenericServices(this IServiceCollection services, Assembly assembly, Type openGenericType)
        {
            var types = assembly.GetTypes()
                .Where(type => type.IsClass
                               && !type.IsAbstract
                               && (!type.IsGenericType || !type.IsGenericTypeDefinition)
                               && type.GetInterfaces()
                                   .Any(i => i.IsGenericType && i.GetGenericTypeDefinition() == openGenericType)); //NOSONAR

            foreach (var type in types)
            {
                foreach (var interfaceType in type.GetInterfaces())
                {
                    services.AddTransient(interfaceType, type);
                }
            }
        }

        /// <summary>
        /// Register core services for running new web node.
        /// </summary>
        /// <param name="services"></param>
        /// <returns></returns>
        public static IServiceCollection RegisterMMSCoreServices(this IServiceCollection services)
        {
            // Register DbContext
            services.AddDbContext<MMSContext>();

            // Register DbContextFactory
            services.AddScoped<DbContextFactory>();

            // Register core services and repositories
            services.CoreRegisterServices();
            services.CoreRegisterRepositories();

            // Register LanguageExecuteService with factory
            services.AddScoped<ILanguageExecuteService, LanguageExecuteService>(sp =>
            {
                var contextFactory = sp.GetRequiredService<DbContextFactory>();
                var languageService = sp.GetRequiredService<ILanguageMasterService>();
                var langId = 0;
                return new LanguageExecuteService(contextFactory, languageService, langId);
            });

            services.AddScoped<ISyncTerminalSetupService, SyncTerminalSetupService>();
            services.AddScoped<IIntegrationPaxstoreAndMMSService, IntegrationPaxstoreAndMMSService>();
            services.AddScoped<IPaxstoreMMS, PaxstoreMMS>();
            services.AddScoped<IIntegrationMessageEventAndMMSService, IntegrationMessageEventAndMmsService>();
            services.AddScoped<ILanguageMasterService, LanguageMasterService>();

            services.AddSignalR(); // For SetupUpgradeHub in the DeviceSetupUpgradeService
            services.CoreConfigureAutoMapper();

            return services;
        }

        public static IServiceCollection AddJwtAuthentication(this IServiceCollection services,
            IConfiguration configuration)
        {
            var secretKey = Environment.GetEnvironmentVariable(Constants.ENV_JWT_SECRET);
            if (string.IsNullOrEmpty(secretKey))
            {
                throw new InvalidOperationException($"{Constants.ENV_JWT_SECRET} environment variable is not set.");
            }
            if (secretKey.Length < 32)
            {
                throw new InvalidOperationException($"{Constants.ENV_JWT_SECRET} must be at least 32 characters long.");
            }

            var jwtSettings = configuration.GetSection("JwtSettings").Get<JwtSettings>() ?? new JwtSettings();
            jwtSettings.SecretKey = secretKey;
            services.Configure<JwtSettings>(options =>
            {
                options.SecretKey = jwtSettings.SecretKey;
                options.Issuer = jwtSettings.Issuer;
                options.Audience = jwtSettings.Audience;
                options.ExpiryInMinutes = jwtSettings.ExpiryInMinutes;
                options.ExpiryInMinutesForTemp = jwtSettings.ExpiryInMinutesForTemp;
            });

            services.AddScoped<IJwtService, JwtService>();
            services.AddScoped<ITokenRevocationService, TokenRevocationService>();

            services.AddMemoryCache();

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = configuration["JwtSettings:Issuer"],
                    ValidAudience = configuration["JwtSettings:Audience"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey))
                };
            });

            services.AddAuthorization(options =>
            {
                options.DefaultPolicy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .RequireClaim("type", "full")
                    .Build();

                options.AddPolicy("TempTokenOnly", policy =>
                    policy.RequireClaim("type", "temp"));

                options.AddPolicy("FullTokenOnly", policy =>
                    policy.RequireClaim("type", "full"));
            });

            return services;
        }
    }
}
