using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.CoreUTI;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using Serilog;
using MMS.Model;
using MMS.Model.ApiModelResponse;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class BaseRepository<T> : IBaseRepository<T> where T : BaseEntity
    {
        protected readonly DbContextFactory ContextFactory;
        private DbSet<T> _entities;
        protected string TableName;
        private bool IsUsingSessionContext;

        /// <summary>
        /// This context is registered by DI, and its lifetime scoped
        /// (Different with the DbContextFactory.GetContext() as that one created context manually (and dispose manually).
        /// So incase you don't mind about IsUsingSessionContext (related to Terminal GUI Setup discard all bussiness), use this ScopeContext
        /// </summary>
        protected MMSContext ScopedMMSContext
        {
            get
            {
                if (_scopedMMSContext == null)
                {
                    throw new InvalidOperationException("You have to call BaseRepository.SetScopeMMSContext before using scopedMMSContext.");
                }
                return _scopedMMSContext;
            }
            private set => _scopedMMSContext = value;
        }

        private bool IsUsingScopedContext => _scopedMMSContext != null && !IsUsingSessionContext;

        /// <summary>
        /// This context is registered by DI, and its lifetime scoped
        /// (Different with the DbContextFactory.GetContext() as that one created context manually (and dispose manually).
        /// So incase you don't mind about IsUsingSessionContext (related to Terminal GUI Setup discard all bussiness), use this ScopeContext
        /// </summary>
        protected DbSet<T> ScopedEntities
        {
            get
            {
                if (_scopedEntities == null)
                {
                    throw new InvalidOperationException("The Db Context is null cause this error, double check DI.");
                }
                return _scopedEntities;
            }
            private set => _scopedEntities = value;
        }

        private MMSContext _scopedMMSContext;
        private DbSet<T> _scopedEntities;


        public BaseRepository(DbContextFactory contextFactory)
        {
            ContextFactory = contextFactory;
        }

        /// <summary>
        /// Using session context instance of default context
        /// </summary>
        public void UsingSessionContext()
        {
            this.IsUsingSessionContext = true;
        }

        /// <summary>
        /// Create DbContext
        /// </summary>
        /// <returns></returns>
        public MMSContext GetContext()
        {
            // Using context from DI for consistency.
            // If you want to use this, call set BaseRepository.SetScopeMMSContext for this instance.
            if (IsUsingScopedContext)
            {
                Console.WriteLine($"BaseRepository.GetContext: Returning scoped context {ScopedMMSContext.GetHashCode()} for {typeof(T).Name}");
                return ScopedMMSContext;
            }

            var context = ContextFactory.GetContext(IsUsingSessionContext);
            Console.WriteLine($"BaseRepository.GetContext: Returning factory context {context.GetHashCode()} for {typeof(T).Name}, IsUsingSessionContext={IsUsingSessionContext}");
            return context;
        }

        #region IRepository<T> Members


        /// <summary>
        /// REMEMBER CALL THIS METHOD AFTER USE THE CONTEXT
        ///
        /// </summary>
        public void CtxDisposeOrNot(MMSContext context)
        {
            // Skip dispose if using scoped context
            if (IsUsingScopedContext) return;

            ContextFactory.CtxDisposeOrNot(context, this.IsUsingSessionContext);
        }

        /// <summary>
        /// Get all with condition
        /// </summary>
        /// <returns></returns>
        public virtual IList<T> GetAll(bool includeDeleted = false, bool includeDeactivated = true, Expression<Func<T, bool>> whereClause = null, params Expression<Func<T, object>>[] includes)
        {
            var context = GetContext();
            IList<T> result;

            {
                var query = context.Set<T>().AsQueryable();

                if (!includeDeleted) query = query.Where(x => x.IsStatus != Constants.DELETE_RECORD && x.IsStatus != Constants.PRE_DELETE_RECORD);
                if (!includeDeactivated) query = query.Where(x => x.IsActive);
                if (whereClause != null) query = query.Where(whereClause);

                if (includes != null)
                {
                    foreach (var item in includes)
                    {
                        if (item != null) query = query.Include(item);
                    }
                }

                result = query.ToList();
                CtxDisposeOrNot(context);
            }

            return result;
        }

        /// <summary>
        /// Get all with condition
        /// Retrieves all entities, prioritizing ChangeTracker entries if available.
        /// </summary>
        /// <returns></returns>
        public virtual IList<T> GetAllV2(bool includeDeleted = false, bool includeDeactivated = true, Expression<Func<T, bool>> whereClause = null, params Expression<Func<T, object>>[] includes)
        {
            var context = GetContext();
            IList<T> result;

            {
                var query = context.Set<T>().AsQueryable();
                var changeTrackerData = context.ChangeTracker.Entries<T>().Select(e => e.Entity).AsQueryable();

                if (changeTrackerData.Any())
                {
                    query = changeTrackerData;
                }

                if (!includeDeleted) query = query.Where(x => x.IsStatus != Constants.DELETE_RECORD && x.IsStatus != Constants.PRE_DELETE_RECORD);
                if (!includeDeactivated) query = query.Where(x => x.IsActive);
                if (whereClause != null) query = query.Where(whereClause);

                if (includes != null)
                {
                    foreach (var item in includes)
                    {
                        if (item != null) query = query.Include(item);
                    }
                }

                result = query.ToList();
                CtxDisposeOrNot(context);
            }

            return result;
        }

        /// <summary>
        /// Get first element
        /// </summary>
        /// <returns></returns>
        public virtual T FirstOrDefault(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes)
        {
            var context = GetContext();
            T result;
            {
                var query = context.Set<T>().AsQueryable();

                if (!includeDeleted)
                    query = query.Where(x => x.IsStatus != Constants.DELETE_RECORD && x.IsStatus != Constants.PRE_DELETE_RECORD);

                foreach (var item in includes)
                {
                    query = query.Include(item);
                }

                if (whereClauses != null)
                {
                    result = query.FirstOrDefault(whereClauses);
                }
                else
                {
                    result = query.FirstOrDefault();
                }

                CtxDisposeOrNot(context);
            }


            return result;
        }

        /// <summary>
        /// Get element by ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public virtual T GetById(int id, params Expression<Func<T, object>>[] includes)
        {
            var context = GetContext();
            T result;

            {
                var query = context.Set<T>().Where(p => p.Id == id);
                if (query == null)
                    return default(T);

                foreach (var item in includes)
                {
                    query = query.Include(item);
                }

                result = query.FirstOrDefault();

                CtxDisposeOrNot(context);
            }

            return result;
        }

        /// <summary>
        /// Delete element
        /// </summary>
        /// <param name="entity"></param>
        public virtual void Delete(T entity, bool isDeleteFromData = false)
        {
            var context = GetContext();
            {
                if (isDeleteFromData)
                {
                    context.Set<T>().Remove(entity);
                    context.SaveChanges();
                }
                else
                {
                    entity.IsStatus = MMS.Core.CoreUTI.Constants.DELETE_RECORD;
                    Update(entity);
                }

                CtxDisposeOrNot(context);
            }
        }

        /// <summary>
        /// Delete element by ID
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public virtual bool Delete(int id)
        {
            var obj = GetById(id);
            if (obj != null)
            {
                Delete(obj);
                return true;
            }
            return false;
        }

        public bool Delete(int id, bool isDeleteFromData = false)
        {
            var obj = GetById(id);
            if (obj != null)
            {
                Delete(obj, isDeleteFromData);
                return true;
            }
            return false;
        }

        /// <summary>
        /// Get data multilanguages
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="dics"></param>
        /// <returns></returns>
        public T GetValueByLang(T entity, System.Collections.Generic.IList<Dictionary> dics)
        {
            foreach (var each in dics)
            {
                var prop = entity.GetType().GetProperty(each.Field, BindingFlags.Public | BindingFlags.Instance);
                if (null != prop && prop.CanWrite && !string.IsNullOrWhiteSpace(each.Value))
                {
                    prop.SetValue(entity, each.Value, null);
                }
            }
            return entity;
        }



        /// <summary>
        /// Add Or Update
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="isCommit"></param>
        public virtual void AddOrUpdate(T entity, bool isCommit = true)
        {
            var context = GetContext();
            {
                if (entity.Id == 0)
                {
                    entity.Created = DateTime.Now;
                    entity.Modified = DateTime.Now;
                    context.Set<T>().Add(entity);
                }
                else
                {
                    entity.Modified = DateTime.Now;
                    context.Set<T>().Attach(entity);
                    context.Entry(entity).State = EntityState.Modified;
                }

                if (isCommit) context.SaveChanges();

                CtxDisposeOrNot(context);
            }
        }


        /// <summary>
        /// Add Or Update the list of entities
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="isCommit"></param>
        public virtual void AddOrUpdateAll(IEnumerable<T> entities, bool isCommit = true)
        {
            var context = GetContext();
            {
                foreach (var entity in entities)
                {
                    if (entity.Id == 0)
                    {
                        entity.Created = DateTime.Now;
                        entity.Modified = DateTime.Now;
                        context.Set<T>().Add(entity);
                    }
                    else
                    {
                        entity.Modified = DateTime.Now;
                        context.Set<T>().Attach(entity);
                        context.Entry(entity).State = EntityState.Modified;
                    }
                }

                if (isCommit) context.SaveChanges();

                CtxDisposeOrNot(context);
            }
        }

        /// <summary>
        /// Insert a element
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public T Insert(T entity)
        {
            var context = GetContext();
            {
                entity.Created = DateTime.Now;
                context.Set<T>().Add(entity);
                context.SaveChanges();
                CtxDisposeOrNot(context);
            }
            return entity;
        }


        /// <summary>
        /// Update a element
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public T Update(T entity)
        {
            var context = GetContext();
            {
                entity.Modified = DateTime.Now;
                context.Set<T>().Attach(entity);
                context.Entry(entity).State = EntityState.Modified;
                context.SaveChanges();
                CtxDisposeOrNot(context);
            }
            return entity;
        }

        /// <summary>
        /// Insert a element
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public T Insert(T entity, bool commit = true)
        {
            var context = GetContext();
            {
                entity.Created = DateTime.Now;
                entity.Modified = DateTime.Now;
                context.Set<T>().Add(entity);
                if (commit)
                {
                    context.SaveChanges();
                }
                CtxDisposeOrNot(context);
            }
            return entity;
        }
        public void Insert(IEnumerable<T> entities, bool commit = true)
        {
            var context = GetContext();
            {
                foreach (var entity in entities)
                {
                    entity.Created = DateTime.Now;
                    entity.Modified = DateTime.Now;
                    context.Set<T>().Add(entity);
                    if (commit)
                    {
                        context.SaveChanges();
                    }
                }
                CtxDisposeOrNot(context);
            }
        }



        /// <summary>
        /// Update a element
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public T Update(T entity, bool commit = true)
        {
            var context = GetContext();
            {
                entity.Modified = DateTime.Now;
                context.Entry(entity).State = EntityState.Modified;
                if (commit)
                {
                    context.SaveChanges();
                }
                CtxDisposeOrNot(context);
            }
            return entity;
        }

        /// <summary>
        /// Update the list of entities
        /// </summary>
        /// <param name="entities">The IEnumerable</param>
        public void Update(IEnumerable<T> entities, bool commit = true)
        {
            var context = GetContext();
            {
                foreach (var entity in entities)
                {
                    entity.Modified = DateTime.Now;
                    context.Set<T>().Attach(entity);
                    context.Entry(entity).State = EntityState.Modified;
                    if (commit)
                    {
                        context.SaveChanges();
                    }
                }
                CtxDisposeOrNot(context);
            }
        }

        #endregion

        public bool SetIsUpdated(string table, List<int> ids, bool isUpdated)
        {
            if (ids == null || ids.Count <= 0) return true;

            var whereIdBuilder = new StringBuilder("(");
            for (var i = 0; i < ids.Count; i++)
            {
                if (i > 0)
                {
                    whereIdBuilder.Append(',');
                }
                whereIdBuilder.Append(ids[i]);
            }
            whereIdBuilder.Append(')');

            const string SQL_UPDATE_TEMPLATE = "UPDATE {0} SET IsUpdated = {1} WHERE Id IN {2}";
            var isUpdatedValue = isUpdated ? "true" : "false";
            var sqlString = string.Format(SQL_UPDATE_TEMPLATE, table, isUpdatedValue, whereIdBuilder);

            try
            {
                var context = GetContext();
                {
                    context.Database.ExecuteSqlRaw(sqlString);
                    CtxDisposeOrNot(context);
                }
            }
            catch
            {
                return false;
            }

            return true;
        }


        public bool SetTemplateForTerminal(int id, List<int> terminalIds, string templateColumn)
        {
            if (terminalIds == null || terminalIds.Count <= 0) return true;

            var whereIdBuilder = new StringBuilder("(");
            for (var i = 0; i < terminalIds.Count; i++)
            {
                if (i > 0)
                {
                    whereIdBuilder.Append(',');
                }
                whereIdBuilder.Append(terminalIds[i]); // Use indexing instead of ElementAt
            }
            whereIdBuilder.Append(',');

            const string SQL_UPDATE_TEMPLATE = "UPDATE {0} SET {1} = {2} WHERE Id IN {3}";
            var idValue = id == 0 ? "NULL" : id.ToString();
            var sqlString = string.Format(SQL_UPDATE_TEMPLATE, Constants.TerminalMaster, templateColumn, idValue, whereIdBuilder);

            var context = GetContext();
            {
                context.Database.ExecuteSqlRaw(sqlString);
                CtxDisposeOrNot(context);
            }

            return true;
        }


        public List<TU> RawSqlQuery<TU>(string query, Func<DbDataReader, TU> map)
        {
            var context = GetContext();
            {
                using (var command = context.Database.GetDbConnection().CreateCommand())
                {
                    command.CommandText = query;
                    command.CommandType = CommandType.Text;

                    context.Database.OpenConnection();

                    using (var result = command.ExecuteReader())
                    {
                        var entities = new List<TU>();

                        while (result.Read())
                        {
                            entities.Add(map(result));
                        }

                        CtxDisposeOrNot(context);

                        return entities;
                    }
                }
            }
        }



        /// <summary>
        /// SingleOrDefault
        /// </summary>
        /// <param name="whereClauses"></param>
        /// <param name="includeDeleted"></param>
        /// <param name="includes"></param>
        /// <returns></returns>
        public virtual T SingleOrDefault(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes)
        {
            T result;
            var context = GetContext();
            {
                var query = context.Set<T>().AsQueryable();

                if (!includeDeleted)
                    query = query.Where(x => x.IsStatus != Constants.DELETE_RECORD);

                foreach (var item in includes)
                {
                    query = query.Include(item);
                }

                if (whereClauses != null)
                    result = query.SingleOrDefault(whereClauses);
                else
                    result = query.SingleOrDefault();

                CtxDisposeOrNot(context);
            }

            return result;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="selectClause"></param>
        /// <param name="whereClauses"></param>
        /// <param name="includeDeleted"></param>
        /// <returns></returns>
        public virtual IList<object> Select(Expression<Func<T, object>> selectClause = null, Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes)
        {
            IList<object> result = new List<object>();
            var context = GetContext();

            {
                var query = context.Set<T>().AsQueryable();
                foreach (var item in includes)
                {
                    query = query.Include(item);
                }
                if (!includeDeleted)
                    query = query.Where(x => x.IsStatus != Constants.DELETE_RECORD);

                if (whereClauses != null)
                    query = query.Where(whereClauses);


                if (selectClause == null)
                    result = query.Select(p => p.Id).ToList() as IList<object>;
                else
                    result = query.Select(selectClause).ToList();

                CtxDisposeOrNot(context);
            }

            return result;

        }
        /// <summary>
        /// Last of default
        /// </summary>
        /// <param name="whereClauses"></param>
        /// <param name="includeDeleted"></param>
        /// <param name="includes"></param>
        /// <returns></returns>

        public T LastOrDefault(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes)
        {
            var context = GetContext();
            T result;

            {
                var query = context.Set<T>().AsQueryable();

                if (!includeDeleted)
                {
                    query = query.Where(x => x.IsStatus != Constants.DELETE_RECORD);
                }

                foreach (var item in includes)
                {
                    query = query.Include(item);
                }

                if (whereClauses != null)
                {
                    result = query.LastOrDefault(whereClauses);
                }
                else
                {
                    result = query.LastOrDefault();
                }

                CtxDisposeOrNot(context);
            }

            return result;
        }

        /// <summary>
        /// Undo all changes
        /// </summary>
        public void UndoAll()
        {
            // REPO BASE Using TerminalUndoAll from BaseRepo, this service must using BaseRepo/BaseService.
            //detect all changes (probably not required if AutoDetectChanges is set to true)
            var context = GetContext();
            if (context == null)
                return;
            context.ChangeTracker.DetectChanges();

            //get all entries that are changed
            var entries = context.ChangeTracker.Entries().Where(e => e.State != EntityState.Unchanged).ToList();

            //somehow try to discard changes on every entry
            foreach (var dbEntityEntry in entries)
            {
                switch (dbEntityEntry.State)
                {
                    case EntityState.Modified:
                    case EntityState.Deleted:
                        dbEntityEntry.State = EntityState.Modified; //Revert changes made to deleted entity.
                        dbEntityEntry.State = EntityState.Unchanged;
                        break;
                    case EntityState.Added:
                        dbEntityEntry.State = EntityState.Detached;
                        break;
                }
            }

            ContextFactory.RemoveContext();
        }

        public virtual IQueryable<T> Table
        {
            get
            {
                return Entities;
            }
        }

        /// <summary>
        /// Set the entity to DBContext
        /// </summary>
        private DbSet<T> Entities
        {
            get
            {
                if (_entities == null)
                {
                    _entities = GetContext().Set<T>();
                }

                return _entities;
            }
        }

        public void SaveChanges()
        {
            var context = GetContext();
            context.SaveChanges();
        }

        public async Task<int> SaveChangesAsync()
        {
            var context = GetContext();
            return await context.SaveChangesAsync();
        }

        public bool IsExisted(int id)
        {
            return GetById(id) != null;
        }

        public IQueryable<T> GetPage(IQueryable<T> query, int pageSize, int pageIndex, Expression<Func<T, object>> sortExpression = null)
        {
            if (pageSize > 0 || pageIndex > 0)
            {
                IOrderedQueryable<T> sortedQuery = null;
                if (!typeof(IOrderedQueryable<T>).IsAssignableFrom(query.Expression.Type)) //query is not ordered
                {

                    if (sortExpression != null)
                    {
                        sortedQuery = query.OrderBy(sortExpression);
                    }
                    else
                    {
                        sortedQuery = query.OrderBy(p => p.Id);
                    }

                }
                else
                {
                    sortedQuery = query as IOrderedQueryable<T>;
                }

                return sortedQuery.Skip(pageIndex * pageSize).Take(pageSize);
            }

            return query;
        }

        public static async Task<PagingResponse<TModel>> GetPagingResponseAsync<TModel>(IQueryable<TModel> query, int pageNumber = 1, int pageSize = 10) where TModel : ApiBaseModel
        {
            var response = new PagingResponse<TModel>();

            if (query == null)
                return response;

            response.TotalRecords = await query.CountAsync();

            if (response.TotalRecords == 0)
                return response;

            int index = (pageNumber - 1) * pageSize;

            query = query.Skip(index).Take(pageSize);


            response.Data = await query.ToListAsync();
            return response;
        }
    }

    public static class RepositoryExtension
    {
        public static IQueryable<T> SortByLevel<T>(this IQueryable<T> query) where T : BaseSortEntity
        {
            return query.Select(p => new { p, ZeroSort = p.SortLevel == 0 })
               .OrderBy(p => p.ZeroSort)
               .ThenBy(p => p.p.SortLevel)
               .ThenBy(p => p.p.Name)
               .Select(p => p.p);
        }

        public static IList<T> SortByLevel<T>(this IList<T> query) where T : BaseSortEntity
        {
            return query.OrderBy(
                p => p.SortLevel == 0
                ? int.MaxValue
                : p.SortLevel)
               .ThenBy(p => p.Name).ToList();
        }

        public static int GetMaxSort<T>(this IQueryable<T> query) where T : BaseSortEntity
        {
            return query.Max(p => p.SortLevel);
        }

        public static IList<T> SortByOrderIndex<T>(this IList<T> query) where T : BaseEntitySort
        {
            return query.OrderBy(
                p => p.OrderIndex == 0
                ? int.MaxValue
                : p.OrderIndex).ToList();
        }

    }
}
