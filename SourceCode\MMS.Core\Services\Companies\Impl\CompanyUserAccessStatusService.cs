using Google.Protobuf.WellKnownTypes;
using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Entities.Company;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Services.Companies.Impl
{
    public class CompanyUserAccessStatusService : BaseService<CompanyUserAccessStatus>, ICompanyUserAccessStatusService
    {
        public CompanyUserAccessStatusService(IBaseRepository<CompanyUserAccessStatus> repository, ILanguageExecuteService languageExecuteService) : base(repository, languageExecuteService)
        {
        }

        public IList<CompanyUserAccessStatus> GetSelected(int companyUserAccessId, bool useSelectSort)
        {
            var query = GetAll(p =>
                p.CompanyUserAccessId == companyUserAccessId &&
                p.AccessStatus.IsActive &&
                p.IsStatus != Constants.DELETE_RECORD &&
                p.AccessStatus.IsStatus != Constants.DELETE_RECORD,
                includes: p => p.AccessStatus).AsQueryable();

            query = query.OrderBy(p => p.OrderIndex);

            return query.ToList();
        }

        public IList<CompanyUserAccessStatus> GetSelected(IList<int> companyUserAccessIds)
        {
            var query = GetAll(p =>
                companyUserAccessIds.ToList().Contains(p.CompanyUserAccessId) &&
                p.AccessStatus.IsActive &&
                p.IsStatus != Constants.DELETE_RECORD &&
                p.AccessStatus.IsStatus != Constants.DELETE_RECORD,
                includes: p => p.AccessStatus).AsQueryable();

            query = query.OrderBy(p => p.OrderIndex);

            return query.ToList();
        }

        public async Task<(bool only1Access, int companyId, int accessStatusId)> IsOnlyOneAccessAsync(UserMaster user)
        {
            if (user == null) throw new Exception($"UserMaster could not be null");

            var context = Repo.GetContext();
            {
                var qCompaniesByUser = from b in context.CompanyUserAccesses.AsNoTracking()
                                       where b.UserAccessId == user.Id
                                       where !b.IsDeactivateSelection
                                       where b.IsActive
                                       where b.AccessDateTime < DateTime.UtcNow
                                       where b.ExpiryDateTime > DateTime.UtcNow
                                       select b;

                var companyUserAccessIds = await qCompaniesByUser.Select(p => p.Id).ToListAsync();

                var accessStatusByCompany = from b in context.CompanyUserAccessStatus.AsNoTracking()
                                            where companyUserAccessIds.Contains(b.CompanyUserAccessId)
                                            where b.IsActive
                                            select b;


                var companyCounter = await qCompaniesByUser.CountAsync();
                var accessStatusCounter = await accessStatusByCompany.CountAsync();

                if (companyCounter == 1 && accessStatusCounter == 1)
                {
                    var companyUserAccesses = await qCompaniesByUser.FirstOrDefaultAsync();
                    var companyUserAccessStatus = await accessStatusByCompany.FirstOrDefaultAsync();

                    return (true, companyUserAccesses.CompanyId, companyUserAccessStatus.AccessStatusId);
                }
                else
                {
                    return (false, 0, 0); // Has more than 1 status and company.
                }
            }
        }

        public void UpdateSelected(int companyUserAccessId, List<int> selectedIds, bool useSelectSort)
        {
            var addableList = new List<CompanyUserAccessStatus>();
            var configs = GetSelected(companyUserAccessId, useSelectSort);

            var index = 1;
            var maxIndex = configs.Any() ? configs.Max(p => p.OrderIndex) + 1 : index;
            foreach (var each in selectedIds)
            {
                CompanyUserAccessStatus obj = null;
                obj = configs.FirstOrDefault(p => p.AccessStatusId == each);

                if (obj != null)
                {
                    if (!useSelectSort)
                    {
                        obj.OrderIndex = maxIndex++;
                    }
                }
                else
                {
                    obj = new CompanyUserAccessStatus
                    {
                        IsActive = true,
                        AccessStatusId = each,
                        OrderIndex = maxIndex++,
                        CompanyUserAccessId = companyUserAccessId
                    };

                    addableList.Add(obj);
                }

                if (useSelectSort)
                {
                    obj.OrderIndex = index++;
                }
            }

            var removeList = configs.Where(p => !selectedIds.Contains(p.AccessStatusId)).ToList();

            foreach (var each in removeList)
            {
                Delete(each);
            }

            Delete(removeList);
            InsertAll(addableList);
            UpdateAll(configs);
        }

        public async Task CompanyAssociationAutoGenerate(IList<CompanyUserAccess> companyUserAccessesAdding, int originalCompanyId)
        {
            var context = Repo.GetContext();
            {
                var deleted = Constants.DELETE_RECORD;
                var qDeleting = from companyUserAccessStatus in context.CompanyUserAccessStatus.AsNoTracking()
                                join companyUserAccesses in context.CompanyUserAccesses on companyUserAccessStatus.CompanyUserAccessId equals companyUserAccesses.Id
                                where companyUserAccesses.OriginalCompanyId == originalCompanyId
                                where companyUserAccesses.IsStatus == deleted // Deleted by CompanyUserAccesses
                                where companyUserAccessStatus.IsStatus != deleted // But not deleted in CompanyUserAccessStatus
                                select companyUserAccessStatus;

                // Deleting
                var companyUserAccessDeleting = await qDeleting.ToListAsync();
                Delete(companyUserAccessDeleting);

                var addingList = new List<CompanyUserAccessStatus>();

                foreach (var each in companyUserAccessesAdding)
                {
                    // New record, adding:
                    var companyUserAccessStatusAdding = new CompanyUserAccessStatus();

                    companyUserAccessStatusAdding.AccessStatusId = (int)AccessStatusHardcodeId.CompanyAssociation;
                    companyUserAccessStatusAdding.CompanyUserAccessId = each.Id;
                    companyUserAccessStatusAdding.IsActive = true;

                    addingList.Add(companyUserAccessStatusAdding);
                }

                Repo.Insert(addingList);
            }


        }

        public async Task<CompanyUserAccessStatus> GetAssociated(int companyUserAccessId)
        {
            var accessStatusId = (int)AccessStatusHardcodeId.CompanyAssociation;

            var context = Repo.GetContext();
            {
                var q = from companyUserAccessStatus in context.CompanyUserAccessStatus.AsNoTracking()
                                where companyUserAccessStatus.AccessStatusId == accessStatusId
                                where companyUserAccessStatus.CompanyUserAccessId == companyUserAccessId
                                select companyUserAccessStatus;

                var result = await q.FirstOrDefaultAsync();

                return result;
            }



        }
    }
}
