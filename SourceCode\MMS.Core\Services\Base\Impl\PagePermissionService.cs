using System;
using MMS.Core.CoreUTI;
using MMS.Core.Entities;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using System.Collections.Generic;
using System.Linq;
using MMS.Core.CoreUTI.Enum;

namespace MMS.Core.Services.Base
{
    public class PagePermissionService : BaseService<PagePermission>, IPagePermissionService
    {
        public PagePermissionService(IBaseRepository<PagePermission> pagePermissionRepository, ILanguageExecuteService languageExecuteService)
            : base(pagePermissionRepository, languageExecuteService)
        {
        }

        private void AddGroup(IList<PagePermission> data)
        {
            var cloudAcess = new PagePermission
            {
                Id = Secure.MyTerminalsIOTDeviceManager,
                Function = Secure.MyTerminalsIOTDeviceManager,
                Name = "Cloud Access",
                ViewAction = true,
                EditAction = true,
                AddAction = true,
                ActiveAction = true
            };

            var terminalAcess = new PagePermission
            {
                Id = Secure.FunctionTerminalSetupFunction,
                Function = Secure.FunctionTerminalSetupFunction,
                Name = "Terminal Setup",
                ViewAction = true,
                EditAction = true,
                AddAction = true,
                ActiveAction = true
            };

            //data.Add(cloudAcess);
            data.Add(terminalAcess);
        }

        public void Init(IList<PagePermission> data)
        {
            data = data.GroupBy(p=>p.Id).Select(p=>p.FirstOrDefault(p1 => p1.IsRoot) ?? p.First()).ToList();
                //data = data.Where(p => p.Id != 0).DistinctBy(p => p.Id).ToList();
            var dataFromDatabase = GetAll();
            var listAdd = new List<PagePermission>();

            foreach (var each in data)
            {
                var temp = dataFromDatabase.SingleOrDefault(p => p.Function.Equals(each.Function));
                var isAdd = temp == null;

                temp = temp ?? new PagePermission();

                temp.Id = each.Id;
                temp.Function = each.Function;
                temp.FunctionParent = each.FunctionParent;
                temp.Name = each.Name;
                temp.DataTable = each.DataTable;
                temp.ViewAction = each.ViewAction;
                temp.EditAction = each.EditAction;
                temp.AddAction = each.AddAction;
                temp.ActiveAction = each.ActiveAction;
                temp.Group = each.Group;
                temp.SelectRemoveAction = each.SelectRemoveAction;
                temp.RemoveAction = each.RemoveAction;

                if (isAdd)
                {
                    listAdd.Add(temp);
                }
            }

            var combineList = dataFromDatabase.ToList();
            combineList.AddRange(listAdd);

            var rootFunctions = combineList.Where(p => p.FunctionParent == null);
            foreach (var rootFunction in rootFunctions)
            {
                SetHierarchy(combineList, rootFunction, string.Empty);
            }

            UpdateAll(dataFromDatabase);
            InsertAll(listAdd);

            var all = dataFromDatabase.Concat(listAdd);
            var delete = all.Where(p => data.All(p1 => p1.Function != p.Function));
            Delete(delete, true);
        }

        public IList<PagePermission> GetAllPermission()
        {
            var context = Repo.GetContext();

            var obj = from b in context.PagePermission
                          select b;

            return obj.ToList();
        }

        public PagePermission GetByFunctionKey(int functionKey)
        {
            var context = Repo.GetContext();
            {

                var obj = from b in context.PagePermission
                          where b.Function == functionKey
                          select b;

                return obj.FirstOrDefault();
            }
        }

        /// <summary>
        /// Initialize permissions from MMS.Api
        /// </summary>
        /// <param name="apiPermissions">List of API permissions to add or update</param>
        /// <param name="permissionsToDelete">List of API permissions to delete</param>
        public void InitApiPermissions(IList<PagePermission> apiPermissions, IList<PagePermission> permissionsToDelete)
        {
            // Delete permissions that are no longer needed
            if (permissionsToDelete.Any())
            {
                Delete(permissionsToDelete, true);
            }

            // Get existing API permissions - use a fresh query to avoid tracking conflicts
            var existingApiPermissions = GetAllPermission()
                .Where(p => p.Source == PermissionSource.Api)
                .ToList();

            // Separate permissions to add and update
            var permissionsToAdd = apiPermissions
                .Where(p => !existingApiPermissions.Any(ep => ep.Function == p.Function))
                .ToList();

            // Update existing permissions by modifying the tracked entities directly
            var permissionsToUpdate = new List<PagePermission>();
            foreach (var apiPermission in apiPermissions.Where(p => existingApiPermissions.Any(ep => ep.Function == p.Function)))
            {
                var existingPermission = existingApiPermissions.First(ep => ep.Function == apiPermission.Function);

                // Update the tracked entity's properties with values from the API permission
                existingPermission.Name = apiPermission.Name;
                existingPermission.Group = apiPermission.Group;
                existingPermission.DataTable = apiPermission.DataTable;
                existingPermission.ViewAction = apiPermission.ViewAction;
                existingPermission.AddAction = apiPermission.AddAction;
                existingPermission.EditAction = apiPermission.EditAction;
                existingPermission.RemoveAction = apiPermission.RemoveAction;
                existingPermission.SelectRemoveAction = apiPermission.SelectRemoveAction;
                existingPermission.ActiveAction = apiPermission.ActiveAction;
                existingPermission.IsActive = apiPermission.IsActive;
                existingPermission.FunctionParent = apiPermission.FunctionParent;
                existingPermission.Source = apiPermission.Source;

                permissionsToUpdate.Add(existingPermission);
            }

            // Insert new permissions
            if (permissionsToAdd.Any())
            {
                InsertAll(permissionsToAdd);
            }

            // Update existing permissions (these are already tracked entities)
            if (permissionsToUpdate.Any())
            {
                UpdateAll(permissionsToUpdate);
            }

            // Set hierarchy for all API permissions - use a separate context operation to avoid tracking conflicts
            SetHierarchyForApiPermissions();
        }

        /// <summary>
        /// Set hierarchy for all API permissions in a separate operation to avoid tracking conflicts
        /// </summary>
        private void SetHierarchyForApiPermissions()
        {
            // Get all API permissions in a fresh context to avoid tracking conflicts
            var allApiPermissions = GetAllPermission()
                .Where(p => p.Source == PermissionSource.Api)
                .ToList();

            var rootFunctions = allApiPermissions.Where(p => p.FunctionParent == null);
            foreach (var rootFunction in rootFunctions)
            {
                SetHierarchy(allApiPermissions, rootFunction, string.Empty);
            }

            // Save the updated hierarchy information back to the database
            // Use UpdateAll with commit=false to batch the updates, then commit once
            if (allApiPermissions.Any())
            {
                UpdateAll(allApiPermissions);
            }
        }

        public IList<PagePermission> GetPermissionRestrict(int deactivatedActions, IList<ActionOfFunction> templateActionOfFunctions)
        {
            var functionTemplateId = templateActionOfFunctions.Where(p => p.Action != 0).Select(p => p.Function);
            var templatePermission = GetAllPermission().Where(p => functionTemplateId.Contains(p.Function)).ToList();
            var actionEnum = (Actions)deactivatedActions;

            foreach (var permission in templatePermission)
            {
                if (permission.DataTable != 0 || permission.Group != 0) continue;

                FilterPagePermissionsByParent(permission, templateActionOfFunctions);

                permission.ViewAction = !actionEnum.HasFlag(Actions.ViewAction) && permission.ViewAction;
                permission.EditAction = !actionEnum.HasFlag(Actions.EditAction) && permission.EditAction;
                permission.AddAction = !actionEnum.HasFlag(Actions.AddAction) && permission.AddAction;
                permission.RemoveAction = !actionEnum.HasFlag(Actions.RemoveAction) && permission.RemoveAction;
                permission.SelectRemoveAction = !actionEnum.HasFlag(Actions.SelectRemoveAction) && permission.SelectRemoveAction;
                permission.ActiveAction = !actionEnum.HasFlag(Actions.ActiveAction) && permission.ActiveAction;
            }

            return templatePermission;
        }

        private static void SetHierarchy(List<PagePermission> dataPagePermissions, PagePermission currentFunction, string parentHierarchy)
        {
            currentFunction.HierarchyId = string.IsNullOrEmpty(parentHierarchy) ? currentFunction.Id.ToString() : $"{parentHierarchy},{currentFunction.Id}";

            var sub = dataPagePermissions.Where(p => p.FunctionParent == currentFunction.Id);

            foreach (var permission in sub)
            {
                SetHierarchy(dataPagePermissions, permission, currentFunction.HierarchyId);
            }
        }

        private static void FilterPagePermissionsByParent(PagePermission pagePermission, IList<ActionOfFunction> actionOfFunctions)
        {
            var actionOfFunction = actionOfFunctions.FirstOrDefault(p => p.Function == pagePermission.Function);

            if (actionOfFunction == null)
                return;

            pagePermission.ViewAction = HasActionFlag(actionOfFunction.Action, Actions.ViewAction);
            pagePermission.AddAction = HasActionFlag(actionOfFunction.Action, Actions.AddAction);
            pagePermission.EditAction = HasActionFlag(actionOfFunction.Action, Actions.EditAction);
            pagePermission.ActiveAction = HasActionFlag(actionOfFunction.Action, Actions.ActiveAction);
            pagePermission.SelectRemoveAction = HasActionFlag(actionOfFunction.Action, Actions.SelectRemoveAction);
            pagePermission.RemoveAction = HasActionFlag(actionOfFunction.Action, Actions.RemoveAction);
        }

        private static bool HasActionFlag(int action, Actions actionEnum)
        {
            return ((Actions)action).HasFlag(actionEnum);
        }
    }
}
